# create a ping view
from rest_framework.generics import ListAPIView
from rest_framework.response import Response

from apps.vendors.serializers import VendorSerializer
from apps.users.models import Vendor



class VendorAPIView(ListAPIView):
    serializer_class = VendorSerializer
    queryset = Vendor.objects.all()

    def get(self, request, *args, **kwargs):
        vendors = self.get_queryset()
        serializer = self.serializer_class(vendors, many=True)
        return Response(serializer.data)
