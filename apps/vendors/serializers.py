from rest_framework import serializers

from apps.users.models import Vendor


class VendorSerializer(serializers.ModelSerializer):
    """
    Vendor serializer
    """
    class Meta:
        """
        Meta class
        """
        model = Vendor
        fields = (
            "id",
            "name",
            "logo",
            "region",
            "district",
            "work_start_time",
            "work_end_time",
            "created_at",
            "total_bottles",
            "description"
        )
        read_only_fields = ("created_at",)
