import hashlib
import hmac
import json
from urllib.parse import parse_qsl

from rest_framework import viewsets
from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone
from django.db import transaction
from .serializers import (
    CustomerSerializer,
    PhoneTokenSerializer,
    CustomerCreateSerializer,
    PhoneTokenResendSerializer
)

from apps.users.models import Customer, PhoneToken, User, UserTypes
from apps.users.vendors.models import VendorClient
from apps.users.vendors.serializers import VendorClientSerializer

from apps.users.customers.utils import generate_token
from apps.users.common.utils import create_username
from apps.orders.models import Order
from apps.orders.serializers import OrderSerializer
from rest_framework import serializers


class CustomerViewSet(viewsets.ModelViewSet):
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    http_method_names = ['get', 'post', 'patch']

    def get_queryset(self):
        return self.queryset

    def get_serializer_class(self):
        if self.action == "create":
            return CustomerCreateSerializer
        elif self.action == "verify":
            return PhoneTokenSerializer
        elif self.action == "send_code":
            return PhoneTokenResendSerializer
        elif self.action == "resend":
            return PhoneTokenResendSerializer
        elif self.action == "partial_update":
            return CustomerCreateSerializer
        return CustomerSerializer

    def create(self, request, *args, **kwargs):
        """
        Create customer.
        """
        serializer = self.get_serializer(data=request.data)
        phone_number = serializer.initial_data.get("user", {}).get("phone_number")

        try:
            user = User.objects.get(
                phone_number=phone_number
            )
            if user:
                try:
                    customer, created = Customer.objects.update_or_create(
                        user=user,
                        defaults={
                            "fullname": serializer.initial_data.get("fullname"),
                            "avatar": serializer.initial_data.get("avatar"),
                            "number2": serializer.initial_data.get("number2"),
                            "longitude": serializer.initial_data.get("longitude"),
                            "latitude": serializer.initial_data.get("latitude"),
                            "passport": serializer.initial_data.get("passport"),
                            "address": serializer.initial_data.get("address"),
                            "region_id": serializer.initial_data.get("region"),
                            "district_id": serializer.initial_data.get("district"),
                        }
                    )

                    customer_serializer = CustomerSerializer(customer)
                    user.is_active = True
                    user.save()
                    refresh = RefreshToken.for_user(user)

                    return Response(
                        {
                            "refresh": str(refresh),
                            "access": str(refresh.access_token),
                            "role": user.user_type,
                            "user": customer_serializer.data,
                            "message": "Customer created successfully"
                        },
                        status=status.HTTP_201_CREATED
                    )
                except Customer.DoesNotExist:
                    pass

        except User.DoesNotExist:
            pass


        serializer.is_valid(raise_exception=True)

        phone_number = serializer.validated_data.get("user", {}).get("phone_number")
        if not phone_number:
            return Response(
                {"error": "Phone number is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            existing_customer = Customer.objects.select_related('user').get(
                user__phone_number=phone_number, 
                user__user_type=UserTypes.CUSTOMER
            )
            if existing_customer.user.is_active:
                return Response(
                    {"error": "Customer already exists with this phone number"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Customer.DoesNotExist:
            pass

        try:
            with transaction.atomic():
                customer = serializer.save()

                user = customer.user
                if not user.is_active:
                    user.is_active = True
                    user.save()

                if not hasattr(user, 'customer'):
                    raise Exception("Customer profile not properly linked to user")

                refresh = RefreshToken.for_user(user)
                customer_serializer = CustomerSerializer(customer)

                return Response(
                    {
                        "refresh": str(refresh),
                        "access": str(refresh.access_token),
                        "role": user.user_type,
                        "user": customer_serializer.data,
                        "message": "Customer created successfully"
                    },
                    status=status.HTTP_201_CREATED
                )
        except Exception as e:
            return Response(
                {"error": f"Failed to create customer: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["post"])
    def send_code(self, request, *args, **kwargs):
        """
        Send verification code to phone number.
        """
        phone_number = request.data.get("phone_number")
        if not phone_number:
            return Response(
                {"error": "Phone number is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        generate_token(phone_number)

        return Response(
            {
                "message": "Verification code sent",
                "status": "sent",
                "phone_number": phone_number
            },
            status=status.HTTP_200_OK
        )

    @action(detail=False, methods=["post"])
    def verify(self, request, *args, **kwargs):
        """
        Verify SMS code.
        """
        phone_number = request.data.get("phone_number")
        code = request.data.get("code")

        if not phone_number or not code:
            return Response(
                {"error": "Phone number and code are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        phone_token = PhoneToken.objects.filter(phone_number=phone_number).last()

        if not phone_token or phone_token.token != code or phone_token.expires_at < timezone.now():
            return Response(
                {"error": "Invalid or expired verification code"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Delete the used token
        phone_token.delete()

        try:
            customer = Customer.objects.select_related('user').get(
                user__phone_number=phone_number, 
                user__user_type=UserTypes.CUSTOMER,
                user__is_active=True
            )

            # Customer exists - return login tokens
            refresh = RefreshToken.for_user(customer.user)
            customer_serializer = CustomerSerializer(customer)

            return Response(
                {
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                    "role": customer.user.user_type,
                    "user": customer_serializer.data,
                    "user_exists": True,
                    "message": "Login successful"
                },
                status=status.HTTP_200_OK
            )

        except Customer.DoesNotExist:
            try:
                with transaction.atomic():
                    username = create_username()
                    user, _ = User.objects.update_or_create(
                        phone_number=phone_number,
                        defaults={
                            "username": username,
                            "user_type": UserTypes.CUSTOMER,
                            "is_active": True
                        }
                    )
                    customer, _ = Customer.objects.update_or_create(user=user)

                    refresh = RefreshToken.for_user(user)
                    customer_serializer = CustomerSerializer(customer)

                    return Response(
                        {
                            "refresh": str(refresh),
                            "access": str(refresh.access_token),
                            "role": user.user_type,
                            "user": customer_serializer.data,
                            "user_exists": False,
                            "message": "User created and logged in successfully"
                        },
                        status=status.HTTP_201_CREATED
                    )

            except Exception as e:
                return Response(
                    {"error": f"Failed to create user: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

    @action(detail=False, methods=["get"])
    def track_latest_order_status(self, request):
        user = request.user
        if user.is_authenticated:
            if user.user_type == UserTypes.CUSTOMER:
                customer = user.customer
                latest_order = Order.objects.filter(customer=customer).order_by('-created_at').first()
                if latest_order:
                    return Response({
                        "order_id": latest_order.id,
                        "status": latest_order.status,
                        "product": latest_order.product.title if latest_order.product else None,
                        "quantity": latest_order.quantity,
                        "delivered_at": latest_order.delivered_at,
                    }, status=status.HTTP_200_OK)
                return Response({"message": "No orders found"}, status=status.HTTP_404_NOT_FOUND)
            return Response(status=status.HTTP_403_FORBIDDEN)
        return Response(status=status.HTTP_401_UNAUTHORIZED)

    @action(detail=False, methods=["get"])
    def order_history(self, request, *args, **kwargs):
        """
        Get customer's order history with improved error handling
        """
        try:
            user = request.user
            if not user.is_authenticated:
                return Response(
                    {
                        "status": "error",
                        "message": "Authentication required"
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )

            if user.user_type != UserTypes.CUSTOMER:
                return Response(
                    {
                        "status": "error",
                        "message": "Only customers can access their order history"
                    },
                    status=status.HTTP_403_FORBIDDEN
                )

            customer = user.customer
            orders = Order.objects.filter(customer=customer).order_by('-created_at')
            page = self.paginate_queryset(orders)

            if page is not None:
                serializer = OrderSerializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = OrderSerializer(orders, many=True)
            return Response(
                {
                    "status": "success",
                    "data": serializer.data
                },
                status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while fetching order history",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["post"])
    def resend(self, request, *args, **kwargs):
        """
        Resend verification token with improved error handling
        """
        try:
            serializer = PhoneTokenSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            phone_number = serializer.validated_data.get("phone_number")
            token = generate_token(phone_number)

            if token:
                return Response(
                    {
                        "status": "success",
                        "message": "Verification code has been resent"
                    },
                    status=status.HTTP_200_OK
                )

            return Response(
                {
                    "status": "error",
                    "message": "Failed to send verification code"
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except serializers.ValidationError as e:
            return Response(
                {
                    "status": "error",
                    "message": "Invalid phone number provided",
                    "details": e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while sending verification code",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def vendors(self, request, *args, **kwargs):
        """
        Get customer's vendors with improved error handling
        """
        try:
            user = request.user
            if not user.is_authenticated:
                return Response(
                    {
                        "status": "error",
                        "message": "Authentication required"
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )

            if user.user_type != UserTypes.CUSTOMER:
                return Response(
                    {
                        "status": "error",
                        "message": "Only customers can access their vendors"
                    },
                    status=status.HTTP_403_FORBIDDEN
                )

            customer = user.customer
            clients = VendorClient.objects.filter(customer=customer)
            serializer = VendorClientSerializer(clients, many=True)
            pagination = self.paginate_queryset(serializer.data)
            
            if pagination:
                return self.get_paginated_response(pagination)
            
            return Response(
                {
                    "status": "success",
                    "data": serializer.data
                },
                status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while fetching vendors",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["post"])
    def login_with_telegram(self, request, *args, **kwargs):
        """
        Login customer with Telegram chat ID with improved error handling
        with using initdata
        """
        try:
            init_data = request.data.get('initData')
            if not init_data:
                return Response(
                    {"detail": "initData is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            parsed_data = dict(parse_qsl(init_data))
            received_hash = parsed_data.pop('hash', '')
            if not received_hash:
                return Response(
                    {"detail": "Invalid initData: hash is missing"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            data_check_string = '\n'.join(
                f"{k}={v}" for k, v in sorted(parsed_data.items())
            )

            bot_token = "TODO: settings.CUSTOMER_BOT_TOKEN replace it with real token"

            secret_key = hmac.new(
                "WebAppData".encode(),
                bot_token.encode(),
                hashlib.sha256
            ).digest()

            calculated_hash = hmac.new(
                secret_key,
                data_check_string.encode(),
                hashlib.sha256
            ).hexdigest()

            if calculated_hash != received_hash:
                return Response(
                    {
                        "detail": "Invalid initData: hash verification failed"
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

            user_json = parsed_data.get('user', '{}')
            if not user_json:
                return Response(
                    {"detail": "Invalid initData: user data is missing"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                user_data = json.loads(user_json)
            except json.JSONDecodeError:
                return Response(
                    {"detail": "Invalid user data format"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            telegram_id = user_data.get('id')
            if not telegram_id:
                return Response(
                    {"detail": "Telegram user ID is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                user = User.objects.get(chat_id=str(telegram_id), user_type=UserTypes.CUSTOMER)
                refresh = RefreshToken.for_user(user)

                return Response({
                    "access": str(refresh.access_token),
                    "refresh": str(refresh)
                }, status=status.HTTP_200_OK)
            except User.DoesNotExist:
                return Response(
                    {"detail": "User with this Telegram ID not found"},
                    status=status.HTTP_404_NOT_FOUND
                )
        except Exception as e:
            return Response(
                {"detail": f"Authentication failed: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["post"])
    def bulk_delete(self, request, *args, **kwargs):
        """
        Bulk delete customers with improved error handling
        Only admins can perform bulk delete operations
        """
        try:
            user = request.user
            if not user.is_authenticated:
                return Response(
                    {
                        "status": "error",
                        "message": "Authentication required"
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )

            if user.user_type in [
                 UserTypes.ADMIN,
                 UserTypes.VENDOR,
            ]:
                return Response(
                    {
                        "status": "error",
                        "message": "Only administrators can perform bulk delete operations"
                    },
                    status=status.HTTP_403_FORBIDDEN
                )

            customer_ids = request.data.get('customer_ids', [])
            
            if not customer_ids:
                return Response(
                    {
                        "status": "error",
                        "message": "customer_ids list is required"
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not isinstance(customer_ids, list):
                return Response(
                    {
                        "status": "error",
                        "message": "customer_ids must be a list"
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                customers_to_delete = Customer.objects.filter(id__in=customer_ids)
                found_ids = set(customers_to_delete.values_list('id', flat=True))
                missing_ids = set(customer_ids) - found_ids
                
                if missing_ids:
                    return Response(
                        {
                            "status": "error",
                            "message": f"Customers with IDs {list(missing_ids)} not found",
                            "found_count": len(found_ids),
                            "missing_count": len(missing_ids)
                        },
                        status=status.HTTP_404_NOT_FOUND
                    )
            except Exception as e:
                return Response(
                    {
                        "status": "error",
                        "message": "Invalid customer IDs provided",
                        "details": str(e)
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

            deleted_count = 0
            failed_deletions = []
            
            with transaction.atomic():
                for customer in customers_to_delete:
                    try:
                        if customer.user:
                            customer.user.delete()
                        deleted_count += 1
                    except Exception as e:
                        failed_deletions.append({
                            "customer_id": customer.id,
                            "error": str(e)
                        })

            if failed_deletions:
                return Response(
                    {
                        "status": "partial_success",
                        "message": f"Successfully deleted {deleted_count} customers, {len(failed_deletions)} failed",
                        "deleted_count": deleted_count,
                        "failed_deletions": failed_deletions
                    },
                    status=status.HTTP_207_MULTI_STATUS
                )

            return Response(
                {
                    "status": "success",
                    "message": f"Successfully deleted {deleted_count} customers",
                    "deleted_count": deleted_count
                },
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while performing bulk delete",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
