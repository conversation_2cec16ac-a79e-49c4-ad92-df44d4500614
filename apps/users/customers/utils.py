from apps.users.models import PhoneToken, User
import random
import string
from django.utils import timezone
from core.sms import eskiz


def generate_token(phone_number):
    from django.conf import settings

    PhoneToken.objects.filter(phone_number=phone_number).delete()

    phone_token = PhoneToken.objects.create(phone_number=phone_number)

    IS_TEST_MODE = getattr(settings, 'IS_TEST_MODE', True)

    if IS_TEST_MODE:
        token = "88888"
    else:
        token = "".join(random.choices(string.digits, k=5))

        # Send SMS via Eskiz
        message = "{} - suvol.uz tizimida roʻyxatdan oʻtish uchun tasdiqlash kodi".format(token)
        try:
            phone_str = str(phone_number)
            phone_clean = phone_str[1:] if phone_str.startswith('+') else phone_str
            response = eskiz.send_sms(
                phone_number=int(phone_clean),
                message=message
            )
            print(f"SMS sent to {phone_number}: {token} - Response: {response}")
        except Exception as e:
            print(f"Failed to send SMS to {phone_number}: {e}")

    phone_token.token = token
    phone_token.expires_at = timezone.now() + timezone.timedelta(minutes=5)
    phone_token.save()

    return token


def verify_token(phone_number, token):
    try:
        phone_token = PhoneToken.objects.filter(phone_number=phone_number).last()
        if phone_token and phone_token.token == token and phone_token.expires_at > timezone.now():
            phone_token.is_verified = True
            phone_token.save()
            phone_token.delete()
            user = User.objects.get(phone_number=phone_number)
            user.is_active = True
            user.save()
            return True
        return False
    except (PhoneToken.DoesNotExist, User.DoesNotExist):
        return False
