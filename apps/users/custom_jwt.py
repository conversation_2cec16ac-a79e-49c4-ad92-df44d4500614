import logging

from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework.validators import ValidationError
from apps.users.common.validators import is_valid_uzb_phone_number as isValid
from apps.users.models import User, UserTypes

logger = logging.getLogger(__name__)

class MyTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Custom serializer for JWT token generation.
    Validates user credentials and returns tokens.
    """
    def validate(self, attrs):
        """
        Validate user credentials and return tokens.
        """
        username = attrs.get("username")
        password = attrs.get("password")

        is_used_phone = isValid(username)
        if is_used_phone:
            user = User.objects.get(
                phone_number=username,
                user_type=UserTypes.CUSTOMER
            )
        else:
            user = User.objects.get(
                username=username,
                user_type=UserTypes.CUSTOMER
            )

        attrs["username"] = user.username
        if user:
            if user.check_password(password):
                data = super().validate(attrs)
                refresh = self.get_token(user)
                data["access"] = str(refresh.access_token)
                data["refresh"] = str(refresh)
                data["role"] = user.user_type
                return data
            else:
                raise ValidationError(
                    {"detail": "username/phone number or password is incorrect"}
                )
        else:
            raise ValidationError(
                {"detail": "username/phone number or password is incorrect"}
            )

    def get_token(self, user):
        """
        Get token for user and add username to it.
        """
        token = super().get_token(user)
        token["username"] = user.username
        return token


class DashboardTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Custom serializer for JWT token generation for dashboard access.
    Validates user credentials and returns tokens.
    """
    def validate(self, attrs):
        username = attrs.get("username")
        password = attrs.get("password")

        is_used_phone = isValid(username)
        try:
            if is_used_phone:
                user = User.objects.filter(
                    phone_number=username,
                    user_type__in=[UserTypes.VENDOR, UserTypes.ADMIN]
                ).first()

            else:
                user = User.objects.filter(
                    username=username,
                    user_type__in=[UserTypes.VENDOR, UserTypes.ADMIN]
                ).first()

            if not user:
                raise ValidationError({"detail": "username/phone number or password is incorrect"})

            if not user.check_password(password):
                raise ValidationError({"detail": "username/phone number or password is incorrect"})

            attrs["username"] = user.username
            data = super().validate(attrs)
            refresh = self.get_token(user)
            data["access"] = str(refresh.access_token)
            data["refresh"] = str(refresh)
            data["role"] = user.user_type

            return data

        except Exception as e:
            raise ValidationError({"detail": "username/phone number or password is incorrect"}) from e

    def get_token(self, user):
        token = super().get_token(user)
        token["username"] = user.username
        return token


class MyTokenObtainPairView(TokenObtainPairView):
    """
    Custom view for JWT token generation.
    """
    serializer_class = MyTokenObtainPairSerializer


class DashboardTokenObtainPairView(TokenObtainPairView):
    """
    Custom view for JWT token generation for dashboard access.
    """
    serializer_class = DashboardTokenObtainPairSerializer


class CourierTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Custom serializer for JWT token generation for courier access.
    Validates courier credentials and returns tokens.
    """
    def validate(self, attrs):
        """
        Validate courier credentials and return tokens.
        """
        username = attrs.get("username")
        password = attrs.get("password")

        is_used_phone = isValid(username)
        try:
            # Find courier user
            if is_used_phone:
                user = User.objects.filter(phone_number=username, user_type=UserTypes.COURIER).first()
            else:
                user = User.objects.filter(username=username, user_type=UserTypes.COURIER).first()

            if not user:
                raise ValidationError({"detail": "username/phone number or password is incorrect"})

            # Check if password is correct
            if not user.check_password(password):
                raise ValidationError({"detail": "username/phone number or password is incorrect"})

            # Set username to the actual username for token generation
            attrs["username"] = user.username

            # Generate token
            data = super().validate(attrs)
            refresh = self.get_token(user)
            data["access"] = str(refresh.access_token)
            data["refresh"] = str(refresh)
            data["role"] = user.user_type

            return data

        except Exception as e:
            raise ValidationError({"detail": "username/phone number or password is incorrect"}) from e

    def get_token(self, user):
        token = super().get_token(user)
        token["username"] = user.username
        token["user_type"] = user.user_type
        return token


class CourierTokenObtainPairView(TokenObtainPairView):
    """
    Custom view for JWT token generation for courier access.
    """
    serializer_class = CourierTokenObtainPairSerializer
