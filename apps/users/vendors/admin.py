from django.contrib import admin
from unfold.admin import ModelAdmin
from apps.users.vendors.models import RequestVendor, VendorAvailablePlaces, VendorClient, VendorStats


@admin.register(RequestVendor)
class RequestVendorAdmin(ModelAdmin):
    list_display = ("name", "phone_number", "created_at")
    search_fields = ("name", "phone_number")


@admin.register(VendorAvailablePlaces)
class VendorAvailablePlacesAdmin(ModelAdmin):
    list_display = ("vendor", "region")
    list_filter = ("region",)


@admin.register(VendorClient)
class VendorClientAdmin(ModelAdmin):
    list_display = ("customer", "vendor", "return_bottles")
    list_filter = ("vendor",)


@admin.register(VendorStats)
class VendorStatsAdmin(ModelAdmin):
    list_display = ("vendor", "busy_bottles", "free_bottles")
    search_fields = ("vendor__name",)

