from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from unfold.admin import ModelAdmin
from apps.users.models import (
    User,
    Vendor,
    Customer,
    UserTypes,
    PhoneToken,
    Courier
)
from apps.users.forms import VendorAdminForm, CustomerAdminForm


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ("username", "phone_number", "user_type", "is_staff")


@admin.register(PhoneToken)
class PhoneTokenAdmin(ModelAdmin):
    list_display = ["phone_number", "token", "created_at", "expires_at"]
    search_fields = ["phone_number", "token"]


class VendorAdmin(ModelAdmin):
    form = VendorAdminForm
    list_display = ("name", "created_at", "inn", "region", "district", "image_tag", "total_bottles")
    list_filter = ("region", "is_verified")
    search_fields = ("name", "inn")

    def save_model(self, request, obj, form, change):
        if not obj.user:
            user = User.objects.create(
                phone_number=form.cleaned_data["phone_number"],
                user_type=UserTypes.VENDOR,
            )
            obj.user = user
        obj.save()


class CustomerAdmin(ModelAdmin):
    form = CustomerAdminForm
    list_display = (
        "id",
        "fullname",
        "number1",
        "number2",
        "region",
        "district",
        "passport",
        "image_tag"
    )
    search_fields = ("fullname",)

    def save_model(self, request, obj, form, change):
        if not obj.user:
            user = User.objects.create(
                phone_number=form.cleaned_data["phone_number"],
                user_type=UserTypes.CUSTOMER,
            )
            obj.user = user
        obj.save()


@admin.register(Courier)
class CourierAdmin(ModelAdmin):
    list_display = ('id', 'fullname', 'user', 'address', 'created_at', 'user_type')
    list_filter = ('created_at',)
    search_fields = ('fullname', 'user__phone_number', 'address')
    readonly_fields = ('created_at',)
    filter_horizontal = ('vendors',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


admin.site.register(Vendor, VendorAdmin)
admin.site.register(Customer, CustomerAdmin)
