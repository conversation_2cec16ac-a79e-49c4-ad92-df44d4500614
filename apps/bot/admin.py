"""
bot admin configuration
"""
from django.contrib import admin
from unfold.admin import ModelAdmin
from apps.bot.models import TelegramUser


@admin.register(TelegramUser)
class TelegramUserAdmin(ModelAdmin):
    """
    telegram user admin
    """
    list_display = ('chat_id', 'username', 'first_name', 'last_name', 'user')
    search_fields = ('username', 'chat_id', 'first_name', 'last_name')
    list_filter = ('user',)
    readonly_fields = ('chat_id',)
