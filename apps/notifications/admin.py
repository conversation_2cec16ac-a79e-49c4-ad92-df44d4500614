"""
Notifications app admin configuration.
"""
from django.contrib import admin
from unfold.admin import ModelAdmin
from django.forms import Textarea, ModelForm

from .models import FCMDevice, Notification


class FCMDeviceAdminForm(ModelForm):
    """
    The FCM Device Admin form
    """
    class Meta:
        """
        The meta class options
        """
        model = FCMDevice
        fields = '__all__'
        widgets = {
            'registration_id': Textarea(attrs={'rows': 4}),
        }


@admin.register(FCMDevice)
class FCMDeviceAdmin(ModelAdmin):
    """
    Admin configuration for FCMDevice model.
    """
    form = FCMDeviceAdminForm
    list_display = ('id', 'user', 'token', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('user__username', 'user__phone_number', 'token')
    readonly_fields = ('created_at', 'updated_at')
    actions = ['enable_devices', 'disable_devices']

    def get_list_display(self, request):
        return [
            'user',
            'token',
            'is_active',
            'created_at',
        ]

    def get_list_filter(self, request):
        return [
            'is_active',
            'created_at',
        ]

    def get_search_fields(self, request):
        return [
            'user__username',
            'user__phone_number',
            'token',
        ]

    def enable_devices(self, request, queryset):
        queryset.update(is_active=True)
    enable_devices.short_description = "Enable selected devices"

    def disable_devices(self, request, queryset):
        queryset.update(is_active=False)
    disable_devices.short_description = "Disable selected devices"


class NotificationAdminForm(ModelForm):
    class Meta:
        model = Notification
        fields = '__all__'
        widgets = {
            'body': Textarea(attrs={'rows': 4}),
            'data': Textarea(attrs={'rows': 4}),
        }


@admin.register(Notification)
class NotificationAdmin(ModelAdmin):
    """
    Admin configuration for Notification model.
    """
    form = NotificationAdminForm
    list_display = ('id', 'user', 'title', 'notification_type', 'is_read', 'created_at')
    list_filter = ('is_read', 'notification_type', 'created_at')
    search_fields = ('user__username', 'user__phone_number', 'title', 'body')
    readonly_fields = ('created_at', 'updated_at')
    actions = ['mark_as_read', 'mark_as_unread']
    ordering = ['-created_at']

    def get_list_display(self, request):
        return [
            'user',
            'title',
            'notification_type',
            'is_read',
            'created_at',
        ]

    def get_list_filter(self, request):
        return [
            'is_read',
            'notification_type',
            'created_at',
        ]

    def get_search_fields(self, request):
        return [
            'user__username',
            'user__phone_number',
            'title',
            'body',
        ]

    def mark_as_read(self, request, queryset):
        queryset.update(is_read=True)
    mark_as_read.short_description = "Mark selected notifications as read"

    def mark_as_unread(self, request, queryset):
        queryset.update(is_read=False)
    mark_as_unread.short_description = "Mark selected notifications as unread"
