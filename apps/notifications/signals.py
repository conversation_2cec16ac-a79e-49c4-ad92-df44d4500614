"""
Signal handlers for notifications.
"""
from django.contrib.auth import get_user_model

from apps.notifications.services import FirebaseService
from apps.notifications.models import Notification

User = get_user_model()


class NotificationHelper:
    """
    Helper class for consistent notification handling.
    """

    @staticmethod
    def send_notification(user, title, body, notification_type, data):
        """
        Send a basic notification.
        """
        if not user or not user.fcm_token:
            return False

        try:
            # Create database record
            Notification.objects.create(
                user=user,
                title=title,
                body=body,
                notification_type=notification_type,
                data=data
            )

            # Send FCM notification
            registration_ids = [user.fcm_token] if isinstance(user.fcm_token, str) else user.fcm_token
            FirebaseService().send_notification(
                registration_ids,
                title,
                body,
                data
            )
            return True

        except Exception as e:
            print(f"Notification error: {e}")
            return False
