"""
Order Item serializers for v2 API
"""
from rest_framework import serializers
from apps.orders.models.order_item import OrderItem
from apps.products.serializers.product_v2 import ProductV2ListSerializer


class OrderItemSerializer(serializers.ModelSerializer):
    """
    Serializer for OrderItem model
    """
    product = ProductV2ListSerializer(read_only=True)
    product_id = serializers.UUIDField(write_only=True)
    total_price = serializers.ReadOnlyField()

    class Meta:
        model = OrderItem
        fields = [
            'id', 'product', 'product_id', 'quantity', 
            'unit_price', 'total_price', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'unit_price', 'total_price', 'created_at', 'updated_at']

    def validate_product_id(self, value):
        """Validate that product exists"""
        from apps.products.models.product import Product
        try:
            product = Product.objects.get(id=value)
            return value
        except Product.DoesNotExist:
            raise serializers.ValidationError("Product not found")


class OrderItemCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating OrderItem
    """
    product_id = serializers.UUIDField()

    class Meta:
        model = OrderItem
        fields = ['product_id', 'quantity']

    def validate_product_id(self, value):
        """Validate that product exists and is active"""
        from apps.products.models.product import Product
        try:
            product = Product.objects.select_related('vendor').get(id=value)
            if not product.vendor or not product.vendor.user.is_active:
                raise serializers.ValidationError("Product is not available")
            return value
        except Product.DoesNotExist:
            raise serializers.ValidationError("Product not found")
