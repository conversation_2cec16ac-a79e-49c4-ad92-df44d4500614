"""
Order serializers for v2 API with order items support
"""
from rest_framework import serializers
from django.db import transaction
from django.core.exceptions import ObjectDoesNotExist

from apps.orders.models.order import Order, OrderStatus
from apps.orders.models.order_item import OrderItem
from apps.orders.serializers.order_item import OrderItemSerializer, OrderItemCreateSerializer
from apps.users.models import Customer, UserTypes, User
from apps.users.customers.serializers import CustomerSerializer
from apps.helpers.serializers import RegionSerializer, DistrictSerializer
from apps.products.models.product import Product


class OrderV2CreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating orders with multiple items (v2 API)
    """
    order_items = OrderItemCreateSerializer(many=True, write_only=True)
    total_price = serializers.ReadOnlyField()

    class Meta:
        model = Order
        fields = [
            "id", "customer", "fullname", "order_items", "status",
            "deliverable_time", "returned_bottles", "old_client_return_bottles", 
            "region", "district", "address", "longitude", "latitude", 
            "phone_number", "additional_number", "passport", "total_price", 
            "created_at", "updated_at", "delivered_at", "is_first_order"
        ]
        read_only_fields = [
            "id", "delivered_at", "total_price", "created_at",
            "updated_at", "is_first_order", "status"
        ]

    def validate_order_items(self, value):
        """Validate order items"""
        if not value:
            raise serializers.ValidationError("At least one order item is required")
        
        # Check for duplicate products
        product_ids = [item['product_id'] for item in value]
        if len(product_ids) != len(set(product_ids)):
            raise serializers.ValidationError("Duplicate products are not allowed")
        
        return value

    def validate(self, attrs):
        """Validate that all products belong to the same vendor"""
        order_items = attrs.get('order_items', [])
        if not order_items:
            return attrs

        vendors = set()
        for item in order_items:
            try:
                product = Product.objects.select_related('vendor').get(id=item['product_id'])
                if product.vendor:
                    vendors.add(product.vendor.id)
            except Product.DoesNotExist:
                raise serializers.ValidationError(f"Product {item['product_id']} not found")

        if len(vendors) > 1:
            raise serializers.ValidationError("All products must belong to the same vendor")
        elif len(vendors) == 0:
            raise serializers.ValidationError("No valid vendor found for products")

        return attrs

    @transaction.atomic
    def create(self, validated_data):
        """Create order with order items"""
        order_items_data = validated_data.pop('order_items')
        user = self.context["request"].user

        # Get vendor from first product
        first_product = Product.objects.select_related('vendor').get(
            id=order_items_data[0]['product_id']
        )
        validated_data['vendor'] = first_product.vendor

        if user.user_type == UserTypes.CUSTOMER:
            order = self._create_customer_order(user, validated_data, order_items_data)
        elif user.user_type in [UserTypes.VENDOR, UserTypes.COURIER]:
            order = self._create_vendor_order(user, validated_data, order_items_data)
        else:
            raise serializers.ValidationError(
                {"message": "You do not have permission to create orders"}
            )

        return order

    def _create_customer_order(self, user, validated_data, order_items_data):
        """Create order for customer users with PENDING status"""
        try:
            customer = user.customer
        except Customer.DoesNotExist as exc:
            raise serializers.ValidationError(
                {"message": "Customer profile not found"}
            ) from exc

        validated_data["customer"] = customer
        validated_data["status"] = OrderStatus.PENDING
        
        # Create order without product and quantity (v1 fields)
        order = Order.objects.create(**validated_data)
        
        # Create order items
        self._create_order_items(order, order_items_data)
        
        return order

    def _create_vendor_order(self, user, validated_data, order_items_data):
        """Create order for vendor and courier users with ACCEPTED status"""
        # Handle customer creation/retrieval logic similar to v1
        customer_id = validated_data.pop("customer", None)
        customer = None

        if customer_id:
            try:
                customer = Customer.objects.get(id=customer_id)
            except Customer.DoesNotExist:
                user_obj = User.objects.create_user(
                    phone_number=validated_data.get("phone_number"),
                    username=f"user_{validated_data.get('phone_number')}",
                    user_type=UserTypes.CUSTOMER
                )
                customer = Customer.objects.create(
                    user=user_obj,
                    fullname=validated_data.get("fullname", ""),
                    passport=validated_data.get("passport", ""),
                    number2=validated_data.get("additional_number", "")
                )

        validated_data["customer"] = customer
        validated_data["status"] = OrderStatus.ACCEPTED

        # Create order
        order = Order.objects.create(**validated_data)
        
        if user.user_type == UserTypes.COURIER:
            try:
                order.courier = user.courier
                order.save()
            except ObjectDoesNotExist as exc:
                raise serializers.ValidationError(
                    {"message": "Your account is not properly set up as a courier. Please contact support."}
                ) from exc

        # Create order items
        self._create_order_items(order, order_items_data)
        
        return order

    def _create_order_items(self, order, order_items_data):
        """Create order items for the order"""
        for item_data in order_items_data:
            product = Product.objects.get(id=item_data['product_id'])
            OrderItem.objects.create(
                order=order,
                product=product,
                quantity=item_data['quantity'],
                unit_price=product.price
            )


class OrderV2Serializer(serializers.ModelSerializer):
    """
    Serializer for reading Order instances with order items (v2 API)
    """
    customer = CustomerSerializer(read_only=True)
    order_items = OrderItemSerializer(many=True, read_only=True)
    region = RegionSerializer(read_only=True)
    district = DistrictSerializer(read_only=True)
    vendor = serializers.SerializerMethodField()
    courier = serializers.SerializerMethodField()
    total_price = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            "id", "customer", "vendor", "courier", "order_items", "status", 
            "returned_bottles", "old_client_return_bottles", "region", "district", 
            "address", "longitude", "latitude", "phone_number", "additional_number", 
            "passport", "total_price", "created_at", "updated_at", "delivered_at", 
            "deliverable_time", "is_first_order", "fullname"
        ]
        read_only_fields = fields

    def get_vendor(self, obj):
        """Get vendor specific fields"""
        if obj.vendor:
            vendor_data = {
                "id": obj.vendor.id,
                "name": obj.vendor.name,
            }
            if hasattr(obj.vendor, 'user') and hasattr(obj.vendor.user, 'phone_number'):
                vendor_data["phone_number"] = obj.vendor.user.phone_number
            return vendor_data
        return None

    def get_courier(self, obj):
        """Get courier specific fields"""
        if obj.courier:
            return {
                "id": obj.courier.id,
                "fullname": obj.courier.fullname,
            }
        return None

    def get_total_price(self, obj):
        """Calculate total price from order items"""
        return sum(item.total_price for item in obj.order_items.all())
