"""
Order Item admin configuration
"""
from django.contrib import admin
from unfold.admin import ModelAdmin
from apps.orders.models.order_item import OrderItem


class OrderItemInline(admin.TabularInline):
    """
    Inline admin for OrderItem in Order admin
    """
    model = OrderItem
    extra = 0
    readonly_fields = ('total_price',)
    fields = ('product', 'quantity', 'unit_price', 'total_price')
    readonly_preprocess_fields = {}


@admin.register(OrderItem)
class OrderItemAdmin(ModelAdmin):
    """
    Admin interface configuration for the OrderItem model.
    """
    list_display = (
        "order",
        "product",
        "quantity",
        "unit_price",
        "total_price",
        "created_at"
    )
    list_filter = ("created_at", "product__vendor")
    search_fields = ("order__id", "product__title")
    readonly_fields = ("total_price",)
    readonly_preprocess_fields = {}
    
    fieldsets = (
        (None, {
            'fields': ('order', 'product', 'quantity', 'unit_price', 'total_price')
        }),
    )
