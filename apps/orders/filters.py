"""
order filters
"""
import django_filters
from apps.orders.models import Order, OrderStatus, get_status_group


class OrderFilter(django_filters.FilterSet):
    """
    the order filter
    """
    status = django_filters.CharFilter(method='filter_status')
    created_at = django_filters.DateFromToRangeFilter(field_name="created_at")
    vendor_id = django_filters.NumberFilter(field_name="vendor__id", lookup_expr="exact")


    class Meta:
        """
        the meta fields
        """
        model = Order
        fields = ["status", "created_at", "vendor_id"]

    def filter_status(self, queryset, name, value):
        """
        the filter order status
        """

        if value in ['active', 'archived']:
            statuses = get_status_group(value)
            return queryset.filter(status__in=statuses)
        elif value in OrderStatus.values:
            return queryset.filter(status=value)
        else:
            return queryset.none()
