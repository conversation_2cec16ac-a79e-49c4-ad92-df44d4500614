"""
Order Item model for v2 API
"""
from django.db import models
from django.core.validators import MinValueValidator
from core.base_models import BaseModel


class OrderItem(BaseModel):
    """
    Model representing individual items in an order (for v2 API)
    """
    order = models.ForeignKey(
        'Order', on_delete=models.CASCADE, related_name='order_items'
    )
    product = models.ForeignKey(
        'products.Product', on_delete=models.CASCADE
    )
    quantity = models.IntegerField(validators=[MinValueValidator(1)])
    unit_price = models.IntegerField(
        help_text="Price per unit at the time of order"
    )

    class Meta:
        verbose_name = "Order Item"
        verbose_name_plural = "Order Items"
        unique_together = ['order', 'product']  # Prevent duplicate products in same order

    def __str__(self):
        return f"{self.order} - {self.product.title} x{self.quantity}"

    @property
    def total_price(self):
        """Calculate total price for this item"""
        if self.quantity is None or self.unit_price is None:
            return 0
        return self.quantity * self.unit_price

    def save(self, *args, **kwargs):
        """Set unit_price from product if not provided"""
        if not self.unit_price:
            self.unit_price = self.product.price
        super().save(*args, **kwargs)
