"""
consultation admin
"""
from django.contrib import admin

from unfold.admin import ModelAdmin

from apps.consultation.models import Consultation


@admin.register(Consultation)
class ConsultationAdmin(ModelAdmin):
    """
    the consultation admin
    """
    list_display = (
        "name",
        "phone_number",
        "region",
        "district",
        "created_at",
    )
    list_filter = ("created_at", "region")
    search_fields = ("name", "phone_number")
    readonly_fields = (
        "name",
        "phone_number",
        "created_at",
        "created_at",
        "region",
        "district",
        "comment"
    )
