from django_filters import rest_framework as filters


from apps.helpers.models import Region


class RegionFilter(filters.FilterSet):
    """
    Filter for Region model
    """
    vendor_id = filters.NumberFilter(field_name='vendors__id', lookup_expr='exact')
    vendor_ids = filters.BaseInFilter(field_name='vendors__id', lookup_expr='in')
    has_vendors = filters.BooleanFilter(method='filter_has_vendors')
    
    def filter_has_vendors(self, queryset, name, value):
        if value:
            return queryset.filter(vendors__isnull=False).distinct()
        return queryset
    
    class Meta:
        model = Region
        fields = ['vendor_id', 'vendor_ids', 'has_vendors']

