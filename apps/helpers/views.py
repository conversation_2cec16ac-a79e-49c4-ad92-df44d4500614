"""
helpers views
"""
from django.core.exceptions import ObjectDoesNotExist

from rest_framework import viewsets
from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db import models

from apps.helpers.models import Region, District
from apps.helpers.serializers import RegionSerializer, DistrictSerializer

from apps.helpers.filters import RegionFilter


class RegionViewSet(viewsets.ModelViewSet):
    """
    the region viewset
    """
    queryset = Region.objects.all()
    serializer_class = RegionSerializer
    filterset_class = RegionFilter

    @action(detail=True, methods=["get"])
    def districts(self, request, pk):
        """
        get districts
        """
        try:
            region = self.get_object()
            districts = District.objects.filter(region=region)
            serializer = DistrictSerializer(districts, many=True)
            return Response(serializer.data)
        except ObjectDoesNotExist as ode:
            return Response(
                {"error": "Object not found", "details": str(ode)},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": "An unexpected error occurred", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
