"""
Category model for products
"""
import uuid
from django.db import models
from django.core.validators import FileExtensionValidator
from core.base_models import BaseModel


class Category(BaseModel):
    """
    Category model for organizing products
    """
    id = models.UUIDField(
        unique=True, default=uuid.uuid4, editable=False, primary_key=True
    )
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    image = models.ImageField(
        null=True,
        blank=True,
        upload_to="categories",
        validators=[FileExtensionValidator(allowed_extensions=["jpeg", "jpg", "png", "webp"])],
    )
    is_active = models.BooleanField(default=True)
    display_order = models.PositiveIntegerField(default=0, help_text="Display order for sorting categories")

    class Meta:
        ordering = ['display_order', 'name']
        verbose_name = "Category"
        verbose_name_plural = "Categories"

    def __str__(self):
        return self.name
