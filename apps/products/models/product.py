"""
The product models
"""
import uuid

from django.core.validators import FileExtensionValidator
from django.db import models

from apps.users.models import Vendor, Customer
from apps.products.validators import is_valid_rating

from core.base_models import BaseModel


class Product(BaseModel):
    """
    The product model
    """
    id = models.UUIDField(
        unique=True, default=uuid.uuid4, editable=False, primary_key=True
    )
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE, null=True, default=None)
    category = models.ForeignKey(
        'Category', on_delete=models.SET_NULL, null=True, blank=True,
        help_text="Product category (for v2 API)"
    )
    title = models.CharField(max_length=250)
    capacity = models.FloatField()
    image = models.ImageField(
        null=True,
        upload_to="products",
        validators=[FileExtensionValidator(allowed_extensions=["jpeg", "jpg", "png", "webp"])],
    )
    description = models.TextField()
    price = models.IntegerField()
    rating = models.FloatField(default=0.0)

    class Meta:
        ordering = ("-created_at",)
        verbose_name = "product"
        verbose_name_plural = "Products"

    def __str__(self):
        """
        return the string representation of the product
        """
        return f"{self.id} - {self.title}"

    @property
    def avg_rating(self):
        """
        return the average rating of the product
        """
        return self.comments.aggregate(models.Avg("rating"))["rating__avg"] or 0


class ProductComment(BaseModel):
    """
    The product comment model
    """
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name="comments", null=True, default=None)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, null=True, default=None)
    text = models.TextField()
    rating = models.FloatField(validators=[is_valid_rating])

    class Meta:
        ordering = ("-created_at",)
        verbose_name = "product comment"
        verbose_name_plural = "Product comments"

    def __str__(self):
        """
        return the string representation of the product comment
        """
        return f"{self.customer.fullname} - {self.product.title}"
