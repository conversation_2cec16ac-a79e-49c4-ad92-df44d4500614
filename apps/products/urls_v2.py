"""
Products URLs for v2 API
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter

from apps.products.views.category import CategoryViewSet
from apps.products.views.product_v2 import ProductV2ViewSet

router = DefaultRouter()

router.register("categories", CategoryViewSet, basename="categories-v2")
router.register("products", ProductV2ViewSet, basename="products-v2")

urlpatterns = [
    path("", include(router.urls)),
]
