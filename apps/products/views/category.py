"""
Category views for v2 API
"""
from django_filters.rest_framework import DjangoFilter<PERSON>ackend
from rest_framework import permissions, status, viewsets
from rest_framework.response import Response
from rest_framework import serializers

from apps.products.models.category import Category
from apps.products.serializers.category import CategorySerializer, CategoryListSerializer
from apps.users.models import UserTypes


class CategoryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for the Category model (v2 API)
    """
    queryset = Category.objects.filter(is_active=True)
    serializer_class = CategorySerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['is_active']

    def get_permissions(self):
        """
        Return permission classes based on the action.
        Allow any user to list or retrieve categories, require admin for other actions.
        """
        if self.action in ["list", "retrieve"]:
            return [permissions.AllowAny()]
        return [permissions.IsAuthenticated()]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return CategoryListSerializer
        return CategorySerializer

    def get_queryset(self):
        """Filter categories based on user permissions"""
        queryset = super().get_queryset()
        user = self.request.user

        if not user.is_authenticated:
            # For unauthenticated users, show only active categories
            return queryset.filter(is_active=True)

        if user.user_type == UserTypes.ADMIN:
            # Admins can see all categories including inactive ones
            return Category.objects.all()
        else:
            # Other users can only see active categories
            return queryset.filter(is_active=True)

    def perform_create(self, serializer):
        """Only admin users can create categories"""
        user = self.request.user
        if user.user_type != UserTypes.ADMIN:
            raise serializers.ValidationError(
                {"message": "Only admin users can create categories"}
            )
        serializer.save()

    def perform_update(self, serializer):
        """Only admin users can update categories"""
        user = self.request.user
        if user.user_type != UserTypes.ADMIN:
            raise serializers.ValidationError(
                {"message": "Only admin users can update categories"}
            )
        serializer.save()

    def perform_destroy(self, instance):
        """Only admin users can delete categories"""
        user = self.request.user
        if user.user_type != UserTypes.ADMIN:
            raise serializers.ValidationError(
                {"message": "Only admin users can delete categories"}
            )
        # Soft delete by setting is_active to False
        instance.is_active = False
        instance.save()

    def create(self, request, *args, **kwargs):
        """Create a new category with improved error handling"""
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            response_data = {
                "data": serializer.data
            }
            return Response(
                response_data,
                status=status.HTTP_201_CREATED
            )
        except serializers.ValidationError as e:
            return Response(
                {
                    "status": "error",
                    "message": "Invalid category data provided",
                    "details": e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while creating the category",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
