"""
Product views for v2 API with category support
"""
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import permissions, status, viewsets
from rest_framework.response import Response
from rest_framework import serializers

from apps.products.models import Product
from apps.products.serializers.product_v2 import ProductV2Serializer, ProductV2ListSerializer
from apps.products.permissions import IsVendorProductOwner
from apps.users.models import UserTypes


class ProductV2Filter(DjangoFilterBackend):
    """Custom filter for v2 products"""
    
    class Meta:
        model = Product
        fields = {
            'category': ['exact'],
            'vendor': ['exact'],
            'price': ['gte', 'lte'],
            'capacity': ['gte', 'lte'],
        }


class ProductV2ViewSet(viewsets.ModelViewSet):
    """
    ViewSet for the Product model v2 API with category support
    """
    queryset = Product.objects.all()
    serializer_class = ProductV2Serializer
    permission_classes = [IsVendorProductOwner]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = [
        'category',
        'vendor',
        'vendor__region',
        'vendor__district',
        'vendor__places_available__region',
        'vendor__places_available__districts',
    ]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ProductV2ListSerializer
        return ProductV2Serializer

    def get_queryset(self):
        queryset = super().get_queryset()
        user = self.request.user

        vendor_region = self.request.query_params.get('vendor_region')
        vendor_district = self.request.query_params.get('vendor_district')
        if vendor_region:
            queryset = queryset.filter(vendor__region=vendor_region)
        if vendor_district:
            queryset = queryset.filter(vendor__district=vendor_district)

        if not user.is_authenticated:
            # For unauthenticated users, show only active products
            return queryset.filter(vendor__user__is_active=True)

        if user.user_type == UserTypes.VENDOR:
            # Vendors can only see their own products
            return queryset.filter(vendor=user.vendor)
        elif user.user_type == UserTypes.ADMIN:
            # Admins can see all products
            return queryset
        elif user.user_type == UserTypes.CUSTOMER:
            # Customers can see all active products
            return queryset.filter(vendor__user__is_active=True)
        else:
            # Other users (couriers) can only see active products
            return queryset.filter(vendor__user__is_active=True)

    def get_permissions(self):
        """
        Return permission classes based on the action.
        """
        if self.action in ["list", "retrieve"]:
            return [permissions.AllowAny()]
        return [IsVendorProductOwner()]

    def create(self, request, *args, **kwargs):
        """
        Create a new product with improved error handling for v2 API
        """
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            response_data = {
                "data": serializer.data
            }
            return Response(
                response_data,
                status=status.HTTP_201_CREATED
            )
        except serializers.ValidationError as e:
            return Response(
                {
                    "status": "error",
                    "message": "Invalid product data provided",
                    "details": e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while creating the product",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def update(self, request, *args, **kwargs):
        """
        Update a product with improved error handling for v2 API
        """
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            
            response_data = {
                "data": serializer.data
            }
            return Response(response_data)
        except serializers.ValidationError as e:
            return Response(
                {
                    "status": "error",
                    "message": "Invalid product data provided",
                    "details": e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while updating the product",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
