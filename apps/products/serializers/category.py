"""
Category serializers for v2 API
"""
from rest_framework import serializers
from apps.products.models.category import Category


class CategorySerializer(serializers.ModelSerializer):
    """
    Serializer for Category model
    """
    
    class Meta:
        model = Category
        fields = [
            'id', 'name', 'description', 'image',
            'is_active', 'display_order', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class CategoryListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for category list
    """
    
    class Meta:
        model = Category
        fields = ['id', 'name', 'image', 'display_order']
