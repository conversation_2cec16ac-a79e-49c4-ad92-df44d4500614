"""
Category admin configuration
"""
from django.contrib import admin
from unfold.admin import ModelAdmin
from apps.products.models.category import Category


@admin.register(Category)
class CategoryAdmin(ModelAdmin):
    """
    Admin interface configuration for the Category model.
    """
    list_display = (
        "name",
        "is_active",
        "display_order",
        "created_at"
    )
    list_filter = ("is_active", "created_at")
    search_fields = ("name", "description")
    ordering = ("display_order", "name")
    list_editable = ("is_active", "display_order")
    
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'image')
        }),
        ('Settings', {
            'fields': ('is_active', 'display_order')
        }),
    )
